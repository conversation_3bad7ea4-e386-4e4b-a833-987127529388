{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.Database.Migrations\\FurionTest.Database.Migrations.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.Core\\FurionTest.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.Core\\FurionTest.Core.csproj", "projectName": "FurionTest.Core", "projectPath": "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.Core\\FurionTest.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"E:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Furion": {"target": "Package", "version": "[4.9.7.97, )"}, "Furion.Extras.Authentication.JwtBearer": {"target": "Package", "version": "[4.9.7.97, )"}, "Furion.Extras.ObjectMapper.Mapster": {"target": "Package", "version": "[4.9.7.97, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "E:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.Database.Migrations\\FurionTest.Database.Migrations.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.Database.Migrations\\FurionTest.Database.Migrations.csproj", "projectName": "FurionTest.Database.Migrations", "projectPath": "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.Database.Migrations\\FurionTest.Database.Migrations.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.Database.Migrations\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"E:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.EntityFramework.Core\\FurionTest.EntityFramework.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.EntityFramework.Core\\FurionTest.EntityFramework.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "E:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.EntityFramework.Core\\FurionTest.EntityFramework.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.EntityFramework.Core\\FurionTest.EntityFramework.Core.csproj", "projectName": "FurionTest.EntityFramework.Core", "projectPath": "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.EntityFramework.Core\\FurionTest.EntityFramework.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.EntityFramework.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"E:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.Core\\FurionTest.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\source\\repos\\test\\FurionTest\\FurionTest\\FurionTest.Core\\FurionTest.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "E:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}