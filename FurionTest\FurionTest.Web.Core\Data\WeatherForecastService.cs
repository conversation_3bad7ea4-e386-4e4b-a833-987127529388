using Furion.DependencyInjection;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FurionTest.Web.Core;

public class WeatherForecastService : ISingleton
{
    private static readonly string[] Summaries = new[]
    {
            "Freezing", "Bracing", "Chi<PERSON>", "<PERSON>", "Mild", "Warm", "<PERSON><PERSON><PERSON>", "<PERSON>", "Sweltering", "Scorching"
        };

    public Task<WeatherForecast[]> GetForecastAsync(DateTime startDate)
    {
        var rng = new Random();
        return Task.FromResult(Enumerable.Range(1, 5).Select(index => new WeatherForecast
        {
            Date = startDate.AddDays(index),
            TemperatureC = rng.Next(-20, 55),
            Summary = Summaries[rng.Next(Summaries.Length)]
        }).ToArray());
    }
}
