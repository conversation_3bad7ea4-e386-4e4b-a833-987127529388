﻿<Project Sdk="Microsoft.NET.Sdk">


	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<NoWarn>1701;1702;1591</NoWarn>
		<DocumentationFile>FurionTest.Application.xml</DocumentationFile>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="applicationsettings.json" />
		<None Remove="FurionTest.Application.xml" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="applicationsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\FurionTest.Core\FurionTest.Core.csproj" />
	</ItemGroup>

</Project>
