﻿@page "/"
@inject ISystemService systemService

<div style="text-align:center;margin-top:50px;">
    <img src="images/logo.png" style="height:100px;margin-bottom:15px;" />
    <div align="center">
        <p><a href="https://gitee.com/dotnetchina/Furion/stargazers"><img src="https://gitee.com/dotnetchina/Furion/badge/star.svg?theme=gvp" alt="star"></a> <a href="https://gitee.com/dotnetchina/Furion/members"><img src="https://gitee.com/dotnetchina/Furion/badge/fork.svg?theme=gvp" alt="fork"></a> <a href="https://github.com/MonkSoul/Furion/stargazers"><img src="https://img.shields.io/github/stars/MonkSoul/Furion?logo=github" alt="GitHub stars"></a> <a href="https://github.com/MonkSoul/Furion/network"><img src="https://img.shields.io/github/forks/MonkSoul/Furion?logo=github" alt="GitHub forks"></a> <a href="https://github.com/MonkSoul/Furion/blob/main/LICENSE"><img src="https://img.shields.io/badge/license-MIT-orange" alt="GitHub license"></a> <a href="https://www.nuget.org/packages/Furion"><img src="https://img.shields.io/nuget/v/Furion.svg?cacheSeconds=10800" alt="nuget"></a></p>
    </div>
    <p>@systemService.GetDescription()</p>
    <p><a href="/api">API 接口</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://gitee.com/dotnetchina/Furion" target="_blank">源码地址</a></p>
</div>
