﻿<Project Sdk="Microsoft.NET.Sdk.Web">


	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<SatelliteResourceLanguages>en-US</SatelliteResourceLanguages>
		<PublishReadyToRunComposite>true</PublishReadyToRunComposite>
	</PropertyGroup>


	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>


	<ItemGroup>
		<ProjectReference Include="..\FurionTest.Web.Core\FurionTest.Web.Core.csproj" />
	</ItemGroup>

</Project>
